import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>lock, FiSave, FiArrowLeft, FiMove, FiUpload } from 'react-icons/fi';
import apiClient from '../api/apiClient';
import TranscriptSegmentEditor from '../components/TranscriptSegmentEditor';
import TranscriptPreview from '../components/TranscriptPreview';
import TranscriptImporter from '../components/TranscriptImporter';
import AdvancedTranscriptEditor from '../components/AdvancedTranscriptEditor';
import type { WordTiming } from '../utils/timeUtils';

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  words?: WordTiming[];
}

const CreateVideo = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [duration, setDuration] = useState<number>(0);
  const [durationMinutes, setDurationMinutes] = useState<string>('');
  const [durationSeconds, setDurationSeconds] = useState<string>('');
  const [fullTranscript, setFullTranscript] = useState('');
  const [segments, setSegments] = useState<TranscriptSegment[]>([]);
  const [currentSegment, setCurrentSegment] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragType, setDragType] = useState<'start' | 'end' | null>(null);
  const [dragSegmentIndex, setDragSegmentIndex] = useState<number | null>(null);
  const [transcriptMode, setTranscriptMode] = useState<'simple' | 'timestamped' | 'advanced'>('simple');
  const [advancedWordTimings, setAdvancedWordTimings] = useState<WordTiming[]>([]);
  const [showTimestampHelp, setShowTimestampHelp] = useState(false);
  const [showImporter, setShowImporter] = useState(false);
  const timelineRef = useRef<HTMLDivElement>(null);
  const segmentRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Update total duration when minutes or seconds change
  useEffect(() => {
    const mins = parseInt(durationMinutes) || 0;
    const secs = parseInt(durationSeconds) || 0;
    setDuration(mins * 60 + secs);
  }, [durationMinutes, durationSeconds]);

  // Process transcript into segments when full transcript changes
  const processTranscript = () => {
    if (!fullTranscript.trim()) {
      setSegments([]);
      return;
    }

    // Split by sentences (simple implementation - split by periods, question marks, exclamation points)
    const sentences = fullTranscript
      .split(/(?<=[.!?])\s+/)
      .filter(sentence => sentence.trim().length > 0);

    // Create initial segments with evenly distributed times
    const totalDuration = duration;
    const segmentDuration = totalDuration / sentences.length;

    const newSegments = sentences.map((sentence, index) => {
      const startTime = Math.round(index * segmentDuration);
      const endTime = Math.round((index + 1) * segmentDuration);
      
      // Process words within the segment
      const words = sentence.trim().split(/\s+/).filter(word => word.length > 0);
      const wordDuration = (endTime - startTime) / words.length;
      
      const wordTimings: WordTiming[] = words.map((word, wordIndex) => {
        const wordStartTime = startTime + (wordIndex * wordDuration);
        const wordEndTime = wordIndex === words.length - 1 
          ? endTime 
          : startTime + ((wordIndex + 1) * wordDuration);
        
        return {
          word,
          startTime: parseFloat(wordStartTime.toFixed(2)),
          endTime: parseFloat(wordEndTime.toFixed(2))
        };
      });
      
      return {
        id: `segment-${index}`,
        text: sentence.trim(),
        startTime,
        endTime,
        words: wordTimings
      };
    });

    setSegments(newSegments);
  };

  // Format seconds to MM:SS format
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Update segment time
  const updateSegmentTime = (index: number, isStart: boolean, newTimeInSeconds: number) => {
    setSegments(prevSegments => {
      const newSegments = [...prevSegments];
      const segment = {...newSegments[index]};
      const oldStartTime = segment.startTime;
      const oldEndTime = segment.endTime;
      const oldDuration = oldEndTime - oldStartTime;
      
      if (isStart) {
        // Ensure start time doesn't exceed end time
        segment.startTime = Math.min(newTimeInSeconds, segment.endTime - 1);
        
        // If not the first segment, ensure it doesn't overlap with previous segment
        if (index > 0) {
          segment.startTime = Math.max(segment.startTime, newSegments[index - 1].endTime);
        }
      } else {
        // Ensure end time is greater than start time
        segment.endTime = Math.max(newTimeInSeconds, segment.startTime + 1);
        
        // If not the last segment, ensure it doesn't overlap with next segment
        if (index < newSegments.length - 1) {
          segment.endTime = Math.min(segment.endTime, newSegments[index + 1].startTime);
        }
      }
      
      // Update word timings proportionally
      if (segment.words && segment.words.length > 0) {
        const newDuration = segment.endTime - segment.startTime;
        const ratio = newDuration / oldDuration;
        
        segment.words = segment.words.map((word, wordIndex) => {
          // Calculate relative positions for each word
          const wordRelativeStart = word.startTime - oldStartTime;
          const wordRelativeEnd = word.endTime - oldStartTime;
          
          const newWordStart = segment.startTime + (wordRelativeStart * ratio);
          const newWordEnd = wordIndex === segment.words!.length - 1 
            ? segment.endTime 
            : segment.startTime + (wordRelativeEnd * ratio);
          
          return {
            ...word,
            startTime: parseFloat(newWordStart.toFixed(2)),
            endTime: parseFloat(newWordEnd.toFixed(2))
          };
        });
      }
      
      newSegments[index] = segment;
      return newSegments;
    });
  };
  
  // Handle dragging segment markers
  const handleTimelineDragStart = (_e: React.MouseEvent, index: number, type: 'start' | 'end') => {
    setIsDragging(true);
    setDragType(type);
    setDragSegmentIndex(index);
    document.addEventListener('mousemove', handleTimelineDragMove);
    document.addEventListener('mouseup', handleTimelineDragEnd);
  };
  
  const handleTimelineDragMove = (e: MouseEvent) => {
    if (!isDragging || dragSegmentIndex === null || !timelineRef.current || dragType === null) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const position = (e.clientX - rect.left) / rect.width;
    const timeInSeconds = Math.max(0, Math.min(Math.round(position * duration), duration));
    
    updateSegmentTime(dragSegmentIndex, dragType === 'start', timeInSeconds);
  };
  
  const handleTimelineDragEnd = () => {
    setIsDragging(false);
    setDragType(null);
    setDragSegmentIndex(null);
    document.removeEventListener('mousemove', handleTimelineDragMove);
    document.removeEventListener('mouseup', handleTimelineDragEnd);
  };



  // Helper functions for transcript management
  const addNewSegment = () => {
    const lastSegment = segments[segments.length - 1];
    const startTime = lastSegment ? lastSegment.endTime : 0;
    const endTime = Math.min(startTime + 5, duration); // Default 5-second segment

    const newSegment: TranscriptSegment = {
      id: `segment-${Date.now()}`,
      text: '',
      startTime,
      endTime,
      words: []
    };

    setSegments([...segments, newSegment]);
    setCurrentSegment(segments.length);
    setTranscriptMode('timestamped');
  };

  const updateSegment = (index: number, updatedSegment: TranscriptSegment) => {
    const newSegments = [...segments];
    newSegments[index] = updatedSegment;
    setSegments(newSegments);

    // Update full transcript
    const newFullTranscript = newSegments.map(seg => seg.text).join(' ');
    setFullTranscript(newFullTranscript);
  };

  const deleteSegment = (index: number) => {
    const newSegments = segments.filter((_, i) => i !== index);
    setSegments(newSegments);

    // Update current segment selection
    if (currentSegment === index) {
      setCurrentSegment(null);
    } else if (currentSegment !== null && currentSegment > index) {
      setCurrentSegment(currentSegment - 1);
    }

    // Update full transcript
    const newFullTranscript = newSegments.map(seg => seg.text).join(' ');
    setFullTranscript(newFullTranscript);
  };

  const formatTimeWithMs = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  };

  const handleImportTranscript = (importedSegments: TranscriptSegment[], importedDuration: number) => {
    setSegments(importedSegments);
    setCurrentSegment(null);
    setTranscriptMode('timestamped');

    // Update full transcript
    const newFullTranscript = importedSegments.map(seg => seg.text).join(' ');
    setFullTranscript(newFullTranscript);

    // Update duration if imported duration is longer
    if (importedDuration > duration) {
      const newMinutes = Math.floor(importedDuration / 60);
      const newSeconds = Math.floor(importedDuration % 60);
      setDurationMinutes(newMinutes.toString());
      setDurationSeconds(newSeconds.toString());
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Title is required');
      return;
    }
    
    if (duration <= 0) {
      setError('Valid duration is required');
      return;
    }
    
    if (transcriptMode === 'advanced') {
      if (advancedWordTimings.length === 0) {
        setError('Transcript is required');
        return;
      }
    } else if (segments.length === 0) {
      setError('Transcript is required');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      let transcriptWithTimestamps;
      let transcriptText;

      if (transcriptMode === 'advanced') {
        // For advanced mode, create a single segment with all word timings
        transcriptText = advancedWordTimings.map(wt => wt.word).join(' ');
        transcriptWithTimestamps = [{
          text: transcriptText,
          startTime: 0,
          endTime: duration,
          words: advancedWordTimings.map(word => ({
            word: word.word,
            startTime: word.startTime,
            endTime: word.endTime
          }))
        }];
      } else {
        // For simple and timestamped modes, use existing segments
        transcriptText = fullTranscript;
        transcriptWithTimestamps = segments.map(segment => ({
          text: segment.text,
          startTime: segment.startTime,
          endTime: segment.endTime,
          words: segment.words?.map(word => ({
            word: word.word,
            startTime: word.startTime,
            endTime: word.endTime
          })) || []
        }));
      }

      // Create video data object
      const videoData = {
        title,
        description,
        duration,
        transcript: transcriptText,
        transcriptSegments: transcriptWithTimestamps
      };

      console.log('Sending video data:', JSON.stringify(videoData, null, 2));

      // Send to API
      await apiClient.post('/videos', videoData);
      
      // Redirect to home page on success
      navigate('/');
    } catch (err: any) {
      console.error('Error creating video:', err);
      console.error('Error response:', err.response?.data);
      const errorMessage = err.response?.data?.message || err.response?.data || 'Failed to create video. Please try again.';
      setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/')}
          className="mr-4 p-2 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          aria-label="Back to home"
        >
          <FiArrowLeft className="h-5 w-5" />
        </button>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add New Video</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-400 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Video Details Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Video Details</h2>
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter video title"
                  required
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter video description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Duration *
                </label>
                <div className="flex items-center">
                  <div className="flex items-center">
                    <input
                      type="number"
                      value={durationMinutes}
                      onChange={(e) => setDurationMinutes(e.target.value)}
                      min="0"
                      className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Min"
                    />
                    <span className="mx-2 text-gray-700 dark:text-gray-300">min</span>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="number"
                      value={durationSeconds}
                      onChange={(e) => setDurationSeconds(e.target.value)}
                      min="0"
                      max="59"
                      className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Sec"
                    />
                    <span className="mx-2 text-gray-700 dark:text-gray-300">sec</span>
                  </div>
                  <div className="ml-2 text-gray-500 dark:text-gray-400 flex items-center">
                    <FiClock className="h-5 w-5 mr-1" />
                    <span>Total: {formatTime(duration)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transcript Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Transcript</h2>

            {/* Transcript Input Mode Toggle */}
            <div className="mb-4">
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setTranscriptMode('simple')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    transcriptMode === 'simple'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  Simple Text
                </button>
                <button
                  type="button"
                  onClick={() => setTranscriptMode('advanced')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    transcriptMode === 'advanced'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  Word-Level Timing
                </button>
                <button
                  type="button"
                  onClick={() => setTranscriptMode('timestamped')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    transcriptMode === 'timestamped'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  Timestamped Format
                </button>
              </div>
            </div>

            {transcriptMode === 'simple' ? (
              /* Simple Transcript Input */
              <div>
                <label htmlFor="transcript" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Full Transcript *
                </label>
                <textarea
                  id="transcript"
                  value={fullTranscript}
                  onChange={(e) => setFullTranscript(e.target.value)}
                  rows={8}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono"
                  placeholder="Enter the full transcript here..."
                  required
                />
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Enter your transcript text. Click "Process Transcript" to automatically divide it into timed segments.
                </div>

                <div className="flex justify-end mt-4">
                  <button
                    type="button"
                    onClick={processTranscript}
                    disabled={!fullTranscript.trim() || duration <= 0}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Process Transcript
                  </button>
                </div>
              </div>
            ) : transcriptMode === 'advanced' ? (
              /* Advanced Word-Level Transcript Editor */
              <div>
                <AdvancedTranscriptEditor
                  duration={duration}
                  onChange={setAdvancedWordTimings}
                  className="mb-4"
                />
              </div>
            ) : (
              /* Timestamped Transcript Input */
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Timestamped Transcript *
                </label>
                <div className="border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800">
                  {/* Transcript Editor Header */}
                  <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Transcript Editor</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {segments.length} segments • {formatTime(duration)} total
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowImporter(true)}
                        className="px-3 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        <FiUpload className="w-3 h-3 inline mr-1" />
                        Import
                      </button>
                      <button
                        type="button"
                        onClick={addNewSegment}
                        className="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                      >
                        Add Segment
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowTimestampHelp(!showTimestampHelp)}
                        className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                      >
                        Help
                      </button>
                    </div>
                  </div>

                  {/* Help Section */}
                  {showTimestampHelp && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border-b border-gray-200 dark:border-gray-700">
                      <div className="text-sm text-blue-800 dark:text-blue-200">
                        <p className="font-medium mb-2">Timestamped Transcript Format:</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          <li>Each segment shows the time range and text</li>
                          <li>Click on timestamps to edit them directly</li>
                          <li>Use the timeline below to visually adjust timing</li>
                          <li>Segments are automatically ordered by start time</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {/* Transcript Segments */}
                  <div className="max-h-96 overflow-y-auto">
                    {segments.length === 0 ? (
                      <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                        <p className="mb-2">No transcript segments yet</p>
                        <p className="text-sm">Add a segment to get started, or switch to Simple Text mode to auto-generate segments</p>
                      </div>
                    ) : (
                      segments.map((segment, index) => (
                        <TranscriptSegmentEditor
                          key={segment.id}
                          segment={segment}
                          index={index}
                          isSelected={currentSegment === index}
                          onSelect={() => setCurrentSegment(index)}
                          onUpdate={(updatedSegment) => updateSegment(index, updatedSegment)}
                          onDelete={() => deleteSegment(index)}
                          duration={duration}
                          previousEndTime={index > 0 ? segments[index - 1].endTime : 0}
                          nextStartTime={index < segments.length - 1 ? segments[index + 1].startTime : duration}
                        />
                      ))
                    )}
                  </div>
                </div>

                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Create and edit transcript segments with precise timing. Each segment represents a portion of the video with specific start and end times.
                </div>
              </div>
            )}
          </div>

          {/* Timeline Editor Section */}
          {segments.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Timeline Preview</h2>

              {/* Compact Timeline visualization */}
              <div className="mb-6">
                <div
                  ref={timelineRef}
                  className="relative h-8 bg-gray-200 dark:bg-gray-700 rounded-md mb-2"
                >
                  {/* Time markers */}
                  {Array.from({ length: Math.ceil(duration / 30) + 1 }).map((_, i) => (
                    <div
                      key={`marker-${i}`}
                      className="absolute top-0 h-full border-l border-gray-400 dark:border-gray-500"
                      style={{ left: `${(i * 30 / duration) * 100}%` }}
                    >
                      <div className="absolute -top-5 -translate-x-1/2 text-xs text-gray-600 dark:text-gray-400">
                        {formatTime(i * 30)}
                      </div>
                    </div>
                  ))}

                  {/* Segment visualizations */}
                  {segments.map((segment, index) => (
                    <div
                      key={segment.id}
                      ref={(el) => { segmentRefs.current[index] = el; return undefined; }}
                      className={`absolute h-full rounded-sm cursor-pointer group transition-colors ${
                        currentSegment === index
                          ? 'bg-blue-500'
                          : 'bg-blue-300 dark:bg-blue-700 hover:bg-blue-400 dark:hover:bg-blue-600'
                      }`}
                      style={{
                        left: `${(segment.startTime / duration) * 100}%`,
                        width: `${Math.max(1, ((segment.endTime - segment.startTime) / duration) * 100)}%`,
                      }}
                      onClick={() => {
                        setCurrentSegment(index);
                      }}
                      title={`Segment ${index + 1}: ${formatTime(segment.startTime)} - ${formatTime(segment.endTime)}`}
                    >
                      {/* Draggable start handle */}
                      <div
                        className="absolute left-0 top-0 h-full w-1 bg-blue-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                        onMouseDown={(e) => handleTimelineDragStart(e, index, 'start')}
                        title="Drag to adjust start time"
                      />

                      {/* Draggable end handle */}
                      <div
                        className="absolute right-0 top-0 h-full w-1 bg-blue-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                        onMouseDown={(e) => handleTimelineDragStart(e, index, 'end')}
                        title="Drag to adjust end time"
                      />
                    </div>
                  ))}
                </div>

                {/* Timeline Legend */}
                <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span>0:00</span>
                  <span className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <div className="w-3 h-3 bg-blue-300 dark:bg-blue-700 rounded-sm mr-1"></div>
                      Segments
                    </span>
                    <span className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-sm mr-1"></div>
                      Selected
                    </span>
                  </span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>

              {/* Transcript-like View */}
              <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">Transcript Timeline</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Click on any segment to edit its timing and content. Segments are displayed in chronological order.
                  </p>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {segments.map((segment, index) => (
                    <div
                      key={segment.id}
                      className={`flex border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-colors ${
                        currentSegment === index
                          ? 'bg-blue-50 dark:bg-blue-900/20'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
                      }`}
                      onClick={() => setCurrentSegment(index)}
                    >
                      {/* Timestamp Column */}
                      <div className="flex-shrink-0 w-32 p-4 border-r border-gray-200 dark:border-gray-700">
                        <div className="text-sm font-mono text-gray-600 dark:text-gray-400">
                          <div className="font-medium text-blue-600 dark:text-blue-400">
                            {formatTimeWithMs(segment.startTime)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-500">
                            to {formatTimeWithMs(segment.endTime)}
                          </div>
                          <div className="text-xs text-gray-400 dark:text-gray-600 mt-1">
                            ({(segment.endTime - segment.startTime).toFixed(1)}s)
                          </div>
                        </div>
                      </div>

                      {/* Content Column */}
                      <div className="flex-1 p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="text-sm text-gray-800 dark:text-gray-200 leading-relaxed">
                              {segment.text || (
                                <span className="text-gray-500 dark:text-gray-400 italic">
                                  No text for this segment
                                </span>
                              )}
                            </div>

                            {/* Progress indicator */}
                            <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                              <div
                                className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                                style={{
                                  width: `${Math.min(100, ((segment.endTime - segment.startTime) / Math.max(duration, 1)) * 100)}%`
                                }}
                              />
                            </div>
                          </div>

                          <div className="ml-4 flex items-center space-x-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              #{index + 1}
                            </span>
                            {currentSegment === index && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

            </div>
          )}

          {/* Transcript Preview Section */}
          {segments.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Transcript Preview</h2>
              <TranscriptPreview
                segments={segments}
                duration={duration}
                title={title}
              />
            </div>
          )}

          {/* Submit button */}
          <div className="flex justify-end pt-4">
            <button
              type="submit"
              disabled={isSubmitting || (transcriptMode !== 'advanced' && segments.length === 0) || (transcriptMode === 'advanced' && advancedWordTimings.length === 0)}
              className="flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiSave className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Video'}
            </button>
          </div>
        </div>
      </form>

      {/* Transcript Importer Modal */}
      {showImporter && (
        <TranscriptImporter
          onImport={handleImportTranscript}
          onClose={() => setShowImporter(false)}
        />
      )}
    </div>
  );
};

export default CreateVideo;
